.bottom {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.role {
  padding: var(--spacing-h);
  border-radius: var(--spacing-1);
    display: flex;
  flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
  gap: var(--spacing-h);
}

.state {
  text-transform: capitalize;
}

.card {
    display: flex;
    flex-direction: column;
    width: 350px;
    justify-content: space-between;
}

.host, .community {
    text-overflow: ellipsis;
    text-wrap-mode: nowrap;
    overflow: hidden;
    max-width: 50%;
}

.image {
    height: 200px;
    object-fit: cover;
}