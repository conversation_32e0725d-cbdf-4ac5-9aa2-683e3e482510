import AccessTimeIcon from "@mui/icons-material/AccessTime"
import {
  <PERSON><PERSON>,
  Box,
  Button,
  ButtonGroup,
  Card,
  CardActions,
  CardContent,
  CardMedia,
  Typography,
} from "@mui/material"
import { useCallback } from "react"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { createImageLink } from "../../../config/images"
import { useEvent } from "../../../hooks/useEvent"
import { COMMUNITY_ROUTE, EVENT_ROUTE_INFO } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { localToLocalWithDay } from "../../../utils/transformTime"
import { LabelTooltip } from "../../elements/LabelTooltip/LabelTooltip"
import { PartyLink } from "../../elements/link/PartyLink/PartyLink"

import * as styles from "./eventThumbnail.module.css"

export interface ICEventHost {
  id: number
  name: string
}

export interface ICEvent {
  id: number
  title: string
  starts: string
  ends?: string | null
  location?: string | null
  maxCapacity?: number | null
  state: string
  role?: string | null
  openness: string
  communities: ICEventHost[]
  reserveCapacity?: number | null
  going?: number | null
  reserve?: number | null
  smallDescription?: string | null
  hasAgeLimit?: boolean | null
  minCapacity?: number | null
  hostId?: number | null
  hostName?: string | null
  image?: string | null
  memberApproval?: boolean | null
}

interface EventThumbnailProps {
  event: ICEvent
}
export const EventThumbnail = ({ event }: EventThumbnailProps) => {
  const { onChangeUserState } = useEvent()
  const user = useUserStore((state) => state.userData)

  const isParticipant = hasPermission(user, "event", "isParticipant", event)

  const isWaiting = hasPermission(user, "event", "isWaiting", event)

  const isRequested = hasPermission(user, "event", "isRequested", event)

  const isReserved = hasPermission(user, "event", "isReserved", event)

  const isHost = hasPermission(user, "event", "isHost", event)

  const canJoin = hasPermission(user, "event", "join", event)

  let status: string | null

  if (isHost) {
    status = "Host"
  } else if (isRequested) {
    status = "Requested"
  } else if (isReserved) {
    status = "Reserved"
  } else if (isWaiting) {
    status = "Interested"
  } else if (isParticipant) {
    status = "Participant"
  } else {
    status = null
  }

  const handleComplete = useCallback(
    () => (status: string | null) => {
      console.log("Complete", status)
    },
    [event.id],
  )

  const onChangeStatus = useCallback(
    (
      status:
        | "participant"
        | "interested"
        | "requested"
        | "reserved"
        | "notgoing",
    ) => {
      onChangeUserState({ event, status, onComplete: handleComplete })
    },
    [event.id],
  )

  const stillHasReserve =
    (event?.maxCapacity ?? 0) > 0 &&
    (event?.going ?? 0) >= (event?.maxCapacity ?? 0) &&
    (event?.reserveCapacity ?? 0) > 0 &&
    (event?.reserve ?? 0) < (event?.reserveCapacity ?? 0)

  let color: string

  switch (event.state) {
    case "hidden":
      color = "error"
      break
    case "open":
      color = "success"
      break
    case "ongoing":
      color = "info"
      break
    case "cancelled":
    case "ended":
    default:
      color = "textDisabled"
      break
  }

  return (
    <Card className={styles.card}>
      <CardMedia
        component="img"
        className={styles.image}
        src={createImageLink("event", "large", event.id, event.image)}
      />
      <CardContent>
        <Box display="flex" flexDirection="column" gap={2}>
          <Box display="flex" gap={1} alignItems="center" flexWrap="nowrap">
            <AccessTimeIcon fontSize="medium" />
            <Typography variant="h6">
              {localToLocalWithDay(event.starts, true)}
            </Typography>
          </Box>
          {event.ends && (
            <Box display="flex" gap={1} alignItems="center" flexWrap="nowrap">
              Till <AccessTimeIcon fontSize="small" />
              <Typography variant="subtitle1">
                {localToLocalWithDay(event.ends, true)}
              </Typography>
            </Box>
          )}
          <Box>
            <Typography variant="subtitle1">{event.title}</Typography>
          </Box>
          {event.hostName && (
            <Box
              display="flex"
              flexDirection="row"
              gap={0.5}
              flexWrap="wrap"
              alignItems="center"
              justifyContent="space-between"
            >
              {event.communities.length > 0 && (
                <PartyLink
                  to={COMMUNITY_ROUTE}
                  color="info"
                  variant="text"
                  className={styles.community}
                  sx={{ minHeight: 0, minWidth: 0, padding: 0 }}
                  params={{ communityId: String(event.communities[0].id) }}
                >
                  {`${event.communities[0].name}`}
                </PartyLink>
              )}
              <Typography
                variant="body2"
                className={styles.host}
              >{`Host: ${event.hostName}`}</Typography>
            </Box>
          )}
          {event.smallDescription && (
            <Box>
              <Typography variant="body1">{event.smallDescription}</Typography>
            </Box>
          )}
          {event.location && (
            <Box>
              <Typography variant="h6" className={styles.state} color={color}>
                {event.location}
              </Typography>
            </Box>
          )}
        </Box>
      </CardContent>
      <CardActions>
        <Box display="flex" flexDirection="column" gap={1} width="100%">
          <Box
            display="flex"
            flexDirection="row"
            gap={1}
            justifyContent="space-between"
            alignItems="center"
          >
            <Box display="flex" flexDirection="row" gap={2} alignItems="center">
              <Typography
                variant="body1"
                className={styles.state}
                color={color}
              >
                {event.state}
              </Typography>
              <Typography variant="body1">
                {`Going: ${event.going} ${event.maxCapacity ? `/${event.maxCapacity}` : ""}`}
              </Typography>
            </Box>
            <Box>
              <Typography variant="body1">
                {`Openness: ${event.openness.toLocaleUpperCase()}`}
              </Typography>
            </Box>
          </Box>
          <Box className={styles.bottom}>
            <ButtonGroup>
              {!isParticipant && canJoin && stillHasReserve && (
                <>
                  <Alert>Only reserve spots left!</Alert>
                  <Button
                    size="small"
                    onClick={() => onChangeStatus("reserved")}
                  >
                    Reserve
                  </Button>
                  <LabelTooltip>
                    <Typography variant="body1">
                      Reserve - You will be added to reserve list. If someone
                      drops out you will be moved to participant list.
                    </Typography>
                  </LabelTooltip>
                </>
              )}
              {!isParticipant && canJoin && !isRequested && !isReserved && (
                <Button
                  size="small"
                  onClick={() =>
                    onChangeStatus(
                      event.memberApproval ? "requested" : "participant",
                    )
                  }
                >
                  {event.memberApproval ? "Request" : "Going"}
                </Button>
              )}
              {!isParticipant && !isWaiting && canJoin && (
                <Button
                  size="small"
                  onClick={() => onChangeStatus("interested")}
                >
                  Interested
                </Button>
              )}
              <PartyLink
                variant="contained"
                to={EVENT_ROUTE_INFO}
                params={{ eventId: String(event.id) }}
              >
                Open Event Page
              </PartyLink>
            </ButtonGroup>
            {status && (
              <Box className={styles.role}>
                <Typography variant="h6" color={isHost ? "error" : "info"}>
                  {status}
                </Typography>
                <LabelTooltip>
                  <Typography variant="body1">
                    Your status in this event:
                  </Typography>
                  <Typography variant="body2">
                    Waiting - You requested participation and are waiting for
                    answer
                  </Typography>
                  <Typography variant="body2">Host - You are a host</Typography>
                  <Typography variant="body2">
                    Participant - You are a participant
                  </Typography>
                </LabelTooltip>
              </Box>
            )}
          </Box>
        </Box>
      </CardActions>
    </Card>
  )
}
