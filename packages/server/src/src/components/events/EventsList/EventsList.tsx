import { Box, Pagination } from "@mui/material"
import { useCallback } from "react"

import { EVENTS_PER_PAGE } from "../../../config/game.conf"
import { EventThumbnail, ICEvent } from "../EventThumbnail/EventThumbnail"

interface EventsListProps {
  events: ICEvent[]
  onPageChange: (page: number) => void
  page?: number
}
export const EventsList = ({ page, events, onPageChange }: EventsListProps) => {
  const onChange = useCallback(
    (_: React.ChangeEvent<unknown>, page: number) => {
      onPageChange(page)
    },
    [onPageChange],
  )

  const pages = Math.ceil(events.length / EVENTS_PER_PAGE)
  const startItem = EVENTS_PER_PAGE * ((page ?? 1) - 1)
  const endItem = startItem + EVENTS_PER_PAGE

  return (
    <Box
      display="flex"
      justifyContent="center"
      flexDirection="column"
      gap={4}
      pt={2}
      pb={8}
    >
      {pages > 1 && (
        <Box
          display="flex"
          justifyContent="center"
          flexDirection="row"
          alignItems="center"
        >
          <Pagination
            shape="rounded"
            count={pages}
            page={page}
            variant="outlined"
            onChange={onChange}
          />
        </Box>
      )}
      <Box display="flex" gap={2} flexWrap="wrap" justifyContent="center">
        {events.slice(startItem, endItem).map((event) => (
          <EventThumbnail key={event.id} event={event} />
        ))}
      </Box>
      {pages > 1 && (
        <Box
          display="flex"
          justifyContent="center"
          flexDirection="row"
          alignItems="center"
          pb={4}
        >
          <Pagination
            shape="rounded"
            count={pages}
            page={page}
            variant="outlined"
            onChange={onChange}
          />
        </Box>
      )}
    </Box>
  )
}
