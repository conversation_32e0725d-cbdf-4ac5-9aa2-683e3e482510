import ReportGmailerrorredIcon from "@mui/icons-material/ReportGmailerrorred"
import { <PERSON>, Card, CardContent, Typography } from "@mui/material"

import { type RoleData } from "../../../../../../common/src/permissions/roles/helpers/types"
import { hasCommunityRole, hasPermission } from "../../../permissions"
import { COMMUNITY_USER_ROUTE } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { PartyLink } from "../../elements/link/PartyLink/PartyLink"
import {
  type ICAvatarUser,
  type ICFAvatarOnClick,
  UserAvatar,
} from "../UserAvatar/UserAvatar"

import * as styles from "./useRow.module.css"

type ICUserRow = ICAvatarUser & {
  roles?: RoleData[]
  gameCount?: number | null
}

interface UserRowProps {
  user: ICUserRow
  communityId: string
  onClick: ICFAvatarOnClick
  labelInfo: string
}
export const UserRow = ({
  user,
  onClick,
  communityId,
  labelInfo,
}: UserRowProps) => {
  const myData = useUserStore((store) => store.userData)
  return (
    <PartyLink
      to={COMMUNITY_USER_ROUTE}
      params={{ userId: user.id.toString(), communityId: String(communityId) }}
      title={user.name}
      aria-label={`Open ${user.name} profile`}
      className={styles.container}
    >
      <Card className={styles.card}>
        <CardContent>
          <Box display="flex" justifyContent="center">
            <UserAvatar
              labelInfo={labelInfo}
              size="large"
              user={user}
              onClick={onClick}
              noFocus
            />
          </Box>
          <Box
            pt={2}
            display="flex"
            justifyContent="center"
            alignItems="center"
            flexDirection="column"
          >
            <Typography variant="h6" className={styles.name}>
              {user.name}
            </Typography>
            {user.gameCount && <Typography>{user.gameCount} games</Typography>}
          </Box>
        </CardContent>
        {hasCommunityRole(user.roles ?? [], "invited", parseInt(communityId)) &&
          hasPermission(myData, "community", "approve", {
            id: parseInt(communityId),
          }) && (
            <Box title="Requires approval" className={styles.invited}>
              <ReportGmailerrorredIcon color="info" fontSize="large" />
            </Box>
          )}
      </Card>
    </PartyLink>
  )
}
