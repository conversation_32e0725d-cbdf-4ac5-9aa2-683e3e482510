import { <PERSON>, Button } from "@mui/material"
import classnames from "classnames"
import React, { useCallback } from "react"

import { gameRoute } from "../../../routes/community/game.route"
import { useUserStore } from "../../../store/useUserStore"
import { OwnerInfo } from "../OwnerInfo/OwnerInfo"
import { UserAvatar } from "../UserAvatar/UserAvatar"

import * as styles from "./ownersList.module.css"

import type { IGameGameUser } from "../../../types/tRPC.types"

interface OwnersListProps {
  userList: IGameGameUser[]
  onViewUser: (user?: number) => void
  onOpenUserProfile: (user: number) => void
  showActive?: boolean
}

export const OwnersList = ({
  onViewUser,
  onOpenUserProfile,
  userList,
  showActive = true,
}: OwnersListProps) => {
  const { userId } = gameRoute.useSearch()
  const myUserId = useUserStore((state) => state.userData.id)

  const handleUserProfileClick = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>, id: number) => {
      event.stopPropagation()
      onOpenUserProfile(id)
    },
    [onOpenUserProfile],
  )

  return (
    <Box
      pb={0}
      gap={0.5}
      display="flex"
      flexDirection="column"
      flexWrap="wrap"
      className={styles.container}
    >
      {userId && showActive && (
        <Box className={styles.userRow}>
          Looking at: {userList.find((user) => user.id === userId)?.name}
        </Box>
      )}
      <Button
        title="Clear user filter"
        variant="text"
        onClick={() => onViewUser()}
      >
        Clear selection
      </Button>
      {userList.map((user) => (
        <Box key={user.id}>
          <Box
            className={classnames(styles.userRow, {
              [styles.selected]: user.id === userId,
            })}
            onClick={() => onViewUser(user.id)}
          >
            <Box display="flex" alignItems="center" gap={2}>
              <UserAvatar user={user} />
              {user.name}
            </Box>
            <Box display="flex" gap={0.5}>
              <Button
                title="Filter user owned items"
                variant="text"
                onClick={() => onViewUser(user.id)}
              >
                Owned
              </Button>
              {myUserId !== user.id && (
                <Button
                  title="Open users profile"
                  variant="outlined"
                  onClick={(event) => handleUserProfileClick(event, user.id)}
                >
                  Profile
                </Button>
              )}
            </Box>
          </Box>
          {user.id === userId && <OwnerInfo user={user} />}
        </Box>
      ))}
    </Box>
  )
}
