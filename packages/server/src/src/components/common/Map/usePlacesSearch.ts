import { useMapsLibrary } from "@vis.gl/react-google-maps"
import { useCallback } from "react"

export interface UsePlaceSearchReturn {
  findPlaceByName: (placeName: string) => Promise<google.maps.places.Place | null>
  findPlaceByCoordinates: (lat: number, lng: number) => Promise<google.maps.places.Place | null>
}

export function usePlaceSearch(): UsePlaceSearchReturn {
  const placesLib = useMapsLibrary("places")

  const findPlaceByName = useCallback(
    async (placeName: string): Promise<google.maps.places.Place | null> => {
      if (!placesLib || !placeName.trim()) return null

      try {
        const { Place } = placesLib
        const request: google.maps.places.SearchByTextRequest = {
          textQuery: placeName,
          fields: [
            "displayName",
            "location",
            "viewport",
            "svgIconMaskURI",
            "iconBackgroundColor",
            "id",
            "formattedAddress",
            "types",
          ],
          maxResultCount: 1,
        }

        const { places } = await Place.searchByText(request)
        
        if (places && places.length > 0) {
          return places[0]
        }
        
        return null
      } catch (error) {
        console.error("Error finding place by name:", error)
        return null
      }
    },
    [placesLib]
  )

  const findPlaceByCoordinates = useCallback(
    async (lat: number, lng: number): Promise<google.maps.places.Place | null> => {
      if (!placesLib || !lat || !lng) return null

      try {
        const { Place } = placesLib
        const location = new google.maps.LatLng(lat, lng)
        
        const request: google.maps.places.SearchNearbyRequest = {
          location,
          radius: 50, // Search within 50 meters
          fields: [
            "displayName",
            "location",
            "viewport",
            "svgIconMaskURI",
            "iconBackgroundColor",
            "id",
            "formattedAddress",
            "types",
          ],
          maxResultCount: 1,
        }

        const { places } = await Place.searchNearby(request)
        
        if (places && places.length > 0) {
          return places[0]
        }

        // If no nearby places found, create a place object from coordinates
        // using reverse geocoding
        return await reverseGeocode(lat, lng)
      } catch (error) {
        console.error("Error finding place by coordinates:", error)
        // Fallback to reverse geocoding
        return await reverseGeocode(lat, lng)
      }
    },
    [placesLib]
  )

  const reverseGeocode = async (lat: number, lng: number): Promise<google.maps.places.Place | null> => {
    try {
      const geocoder = new google.maps.Geocoder()
      const response = await geocoder.geocode({
        location: { lat, lng }
      })

      if (response.results && response.results.length > 0) {
        const result = response.results[0]
        
        // Create a minimal place object from geocoding result
        const place = {
          location: new google.maps.LatLng(lat, lng),
          displayName: result.formatted_address,
          formattedAddress: result.formatted_address,
          id: result.place_id,
          viewport: result.geometry.viewport,
          types: result.types,
          // Default icon properties
          svgIconMaskURI: null,
          iconBackgroundColor: "#4285F4",
        } as google.maps.places.Place

        return place
      }
      
      return null
    } catch (error) {
      console.error("Error in reverse geocoding:", error)
      return null
    }
  }

  return {
    findPlaceByName,
    findPlaceByCoordinates,
  }
}
