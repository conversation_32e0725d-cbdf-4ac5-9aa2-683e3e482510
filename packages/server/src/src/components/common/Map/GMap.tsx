import { Box } from "@mui/material"
import { APIProvider, ControlPosition, Map } from "@vis.gl/react-google-maps"
import { useEffect, useState } from "react"

import { AutocompleteCustom } from "./AutocompleteControl"
import { AutocompleteResult } from "./AutocompleteResult"
import { usePlaceSearch } from "./usePlacesSearch"

type GMapProps = {
  onPlaceSelect?: (place: google.maps.places.Place | null) => void
  place: string
  lat: number
  lng: number
}

export const GMap = ({ place, lat, lng, onPlaceSelect }: GMapProps) => {
  const { findPlaceByName, findPlaceByCoordinates } = usePlaceSearch()
  const [selectedPlace, setSelectedPlace] =
    useState<google.maps.places.Place | null>(null)

  useEffect(() => {
    const loadPlace = async () => {
      let foundPlace: google.maps.places.Place | null = null

      if (place) {
        // Try to find place by name first
        foundPlace = await findPlaceByName(place)
      }

      if (!foundPlace && lat && lng) {
        // Fallback to coordinates if place name doesn't work
        foundPlace = await findPlaceByCoordinates(lat, lng)
      }

      if (foundPlace) {
        setSelectedPlace(foundPlace)
        onPlaceSelect?.(foundPlace)
      }
    }

    loadPlace()
  }, [place, lat, lng, findPlaceByName, findPlaceByCoordinates, onPlaceSelect])

  console.log(selectedPlace)

  return (
    <Box height="100%" width="100%">
      <APIProvider
        apiKey="AIzaSyC6ehAxduYC4T7K3eQYiFLTZuP4n2rvpAo"
        libraries={["marker", "places"]}
      >
        <Map
          mapId={"bf51a910020fa25a"}
          defaultZoom={7}
          defaultCenter={{
            lat: selectedPlace?.location?.lat() ?? 52.520008,
            lng: selectedPlace?.location?.lng() ?? 24.1056,
          }}
          gestureHandling={"greedy"}
          disableDefaultUI
        >
          <AutocompleteCustom
            controlPosition={ControlPosition.TOP_LEFT}
            onPlaceSelect={setSelectedPlace}
          />
          <AutocompleteResult place={selectedPlace} onDrag={setSelectedPlace} />
        </Map>
      </APIProvider>
    </Box>
  )
}
