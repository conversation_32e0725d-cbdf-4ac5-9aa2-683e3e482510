import { Box } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback } from "react"

import { GMap } from "../../../components/common/Map/GMap"
import { createImageLink } from "../../../config/images"
import { eventInfoRoute } from "../../../routes/event/eventInfo.route"
import {
  EVENT_PARTICIPANT_ROUTE,
  EVENT_ROOT_ROUTE,
  EVENT_ROUTE_INFO,
} from "../../../routes/paths"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"

import { HeaderMobile } from "./components/HeaderMobile"
// import { NewGamesView } from "../../community/communityPage/components/NewGamesView"
// import { NewMembersView } from "../../community/communityPage/components/NewMembersView"
// import { Welcome } from "../../community/communityPage/components/Welcome"
import { MobileTabSelect } from "./components/MobileTabSelect"
import * as styles from "./eventInfoPage.module.css"

export const EventInfoPage = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const { games, participants } = eventInfoRoute.useLoaderData() ?? {}

  const search = eventInfoRoute.useSearch()
  const navigate = useNavigate()

  if (!isEvent(event) || !games || !participants) {
    return null
  }

  const onClick = useCallback(
    (id: number) =>
      navigate({
        to: EVENT_PARTICIPANT_ROUTE,
        params: {
          participantId: id.toString(),
          eventId: event.id.toString(),
        },
      }),
    [navigate, event.id],
  )

  const handleChange = useCallback(
    (tab: string) => {
      navigate({
        to: EVENT_ROUTE_INFO,
        params: {
          eventId: event.id.toString(),
        },
        search: {
          tab,
        },
      })
    },
    [navigate, search, event.id],
  )

  const isWelcome = (event.description?.length ?? 0) > 7

  const defaultTab = isWelcome ? "welcome" : "info"

  return (
    <>
      <Box width="400px" height="400px">
        <GMap
          place={event.location ?? "Lativa, Riga"}
          lat={event.lat ?? 52.520008}
          lng={event.lng ?? 24.1056}
        />
      </Box>
      <div>
        <img
          src={createImageLink("event", "cover", event.id, event.image)}
          alt={event.title}
          className={styles.image}
        />
      </div>
      <MobileTabSelect
        tab={search.tab}
        defaultTab={defaultTab}
        onChange={handleChange}
        isWelcome={isWelcome}
      />
      {(search.tab ?? defaultTab) === "info" && <HeaderMobile />}
    </>
  )
}
