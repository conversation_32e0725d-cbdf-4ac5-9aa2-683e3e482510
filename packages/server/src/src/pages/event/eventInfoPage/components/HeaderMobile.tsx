import CardMembershipIcon from "@mui/icons-material/CardMembership"
import MapIcon from "@mui/icons-material/Map"
import { <PERSON><PERSON>, Box, Typography } from "@mui/material"

import { hasPermission } from "../../../../../../../common/src/permissions/hasPermissions"
import { PartyLink } from "../../../../components/elements/link/PartyLink/PartyLink"
import { getEventRole } from "../../../../permissions"
import {
  EVENT_EDIT_ROUTE,
  EVENT_GAMES_ROUTE,
  EVENT_PARTICIPANTS_ROUTE,
  EVENT_PARTICIPANT_ROUTE,
  EVENT_ROOT_ROUTE,
} from "../../../../routes/paths"
import { useUserStore } from "../../../../store/useUserStore"
import { isEvent, useParentRouteData } from "../../../../utils/pages.rootObject"

import * as styles from "./headerMobile.module.css"

export const HeaderMobile = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const userData = useUserStore((state) => state.userData)

  if (!isEvent(event) || !userData) {
    return null
  }

  const role = getEventRole(event.id, userData.roles ?? [])

  const isParticipant = hasPermission(userData, "event", "isParticipant", {
    id: event.id,
  })

  const isWaiting = hasPermission(userData, "event", "isWaiting", {
    id: event.id,
  })

  return (
    <>
      <Box className={styles.titleBox}>
        <Box>
          <Typography variant="subtitle1" color="textSecondary">
            Event:
          </Typography>
          <Typography fontWeight="500" variant="h5" className={styles.name}>
            {event.title}
          </Typography>
        </Box>
        <Box className={styles.titleWrapper}>
          {isParticipant && (
            <Box className={styles.membershipStatus}>
              <Alert
                icon={<CardMembershipIcon color="info" fontSize="medium" />}
                severity="info"
              >
                Participant
              </Alert>
            </Box>
          )}
        </Box>
      </Box>
      <Box className={styles.info}>
        <Box className={styles.location} title={event.openness}>
          <Typography variant="body1" fontWeight="bold">
            Openness:
          </Typography>
          <Typography variant="body1">{event.openness}</Typography>
        </Box>
        {event.location && (
          <Box className={styles.location} title={event.location}>
            <MapIcon />
            <Typography variant="body1">{event.location}</Typography>
          </Box>
        )}
      </Box>
      <Box className={styles.buttonWrapper}>
        <Box>
          <PartyLink
            variant="outlined"
            to={EVENT_GAMES_ROUTE}
            params={{ eventId: event.id.toString() }}
          >
            Games
          </PartyLink>
        </Box>
        <Box>
          <PartyLink
            to={EVENT_PARTICIPANTS_ROUTE}
            params={{ eventId: event.id.toString() }}
            variant="outlined"
          >
            Events
          </PartyLink>
        </Box>
        <Box>
          <PartyLink
            to={EVENT_PARTICIPANT_ROUTE}
            params={{
              eventId: event.id.toString(),
              participantId: (userData.id ?? 0).toString(),
            }}
            variant="outlined"
          >
            Profile
          </PartyLink>
        </Box>
        {hasPermission(userData, "event", "update", event) && (
          <Box>
            <PartyLink
              to={EVENT_EDIT_ROUTE}
              params={{ eventId: event.id.toString() }}
              variant="outlined"
            >
              Edit
            </PartyLink>
          </Box>
        )}
      </Box>
    </>
  )
}
