import { Box } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback } from "react"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { EventFilter } from "../../../components/events/EventFilter/EventFilter"
import { EventsList } from "../../../components/events/EventsList/EventsList"
import { eventsRoute } from "../../../routes/index/events.route"
import { CREATE_EVENT_ROUTE, EVENTS_ROUTE } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"

export const EventsPage = () => {
  const events = eventsRoute.useLoaderData()
  const search = eventsRoute.useSearch()
  const navigate = useNavigate({ from: EVENTS_ROUTE })
  const userInfo = useUserStore((state) => state.userData)

  const onChange = useCallback(
    (page: number) => {
      navigate({
        search: {
          page,
        },
      })

      window.scrollTo({ top: 0 })
    },
    [navigate],
  )

  return (
    <Box>
      <TitleRow title="Events">
        {hasPermission(userInfo, "global", "createEvent") && (
          <Box>
            <PartyLink to={CREATE_EVENT_ROUTE} variant="outlined">
              Create Event
            </PartyLink>
          </Box>
        )}
      </TitleRow>
      <EventFilter from={EVENTS_ROUTE} search={search} />
      {events && (
        <EventsList
          events={events}
          onPageChange={onChange}
          page={search.page}
        />
      )}
    </Box>
  )
}
