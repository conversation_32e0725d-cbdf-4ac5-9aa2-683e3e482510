import { Box, Typography } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback } from "react"

import {
  GameSearch,
  type SearchParams,
} from "../../../components/games/GameSearch/GameSearch"
import { SearchModal } from "../../../components/modals/SearchModal/SearchModal"
import { hasPermission } from "../../../permissions"
import { memberRoute } from "../../../routes/community/member.route"
import {
  COMMUNITIES_ROOT_ROUTE,
  COMMUNITY_USER_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import {
  isCommunity,
  useParentRouteData,
} from "../../../utils/pages.rootObject"

import { MenuBar } from "./components/MenuBar"
import { UserGames } from "./components/UserGames"
import { UserInfo } from "./components/UserInfo"
import * as styles from "./memberPage.module.css"

export const MemberPage = () => {
  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)
  const user = memberRoute.useLoaderData()
  const myData = useUserStore((store) => store.userData)
  const navigate = useNavigate()
  const search = memberRoute.useSearch()
  const params = memberRoute.useParams()

  const onNavigate = useCallback(
    (search: SearchParams) =>
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params,
        search: {
          ...search,
          tab: "games",
        },
      }),
    [navigate, params],
  )

  if (!isCommunity(base) || !user) return null

  const canApprove = hasPermission(myData, "community", "approve", {
    id: base.id,
  })

  const canShare = hasPermission(myData, "community", "shareUserGames", {
    id: base.id,
    viewedUserId: user.id,
    allowShare: base.share,
  })

  return (
    <Box className={styles.container}>
      <MenuBar userId={user.id} communityId={base?.id ?? 0} />
      {(!search.tab || search.tab === "profile") && (
        <Box padding={4}>
          <UserInfo
            canApprove={canApprove}
            user={user}
            canShare={canShare}
            communityId={base?.id ?? 0}
          />
        </Box>
      )}
      {search.tab === "games" && (
        <>
          {user.games ? (
            <Box
              display="flex"
              flexWrap="wrap"
              justifyContent="center"
              alignItems="center"
              flexDirection="column"
              width="100%"
              padding={2}
              boxSizing="border-box"
            >
              <GameSearch
                onNavigate={onNavigate}
                search={search}
                personalOrder
                tags={user.tags}
                tagCategories={user.tagCategories}
              />
              <SearchModal
                onNavigate={onNavigate}
                search={search}
                personalOrder
                tags={user.tags}
                tagCategories={user.tagCategories}
              />
              <UserGames
                games={user.games}
                communityId={base.id}
                tags={user.tags}
              />
            </Box>
          ) : (
            <Box m={4}>
              <Typography>Games are not shared with this community</Typography>
            </Box>
          )}
        </>
      )}
    </Box>
  )
}
