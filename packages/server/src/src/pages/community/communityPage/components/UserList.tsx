import { Box, Typography } from "@mui/material"
import { useNavigate, useParentMatches } from "@tanstack/react-router"
import useEmblaCarousel from "embla-carousel-react"
import { useCallback } from "react"

import { PartyLink } from "../../../../components/elements/link/PartyLink/PartyLink"
import { UserRow } from "../../../../components/user/UserRow/UserRow"
import { displayUsersCount } from "../../../../config/community.conf"
import { communityOpenRoute } from "../../../../routes/community/community.route"
import {
  COMMUNITIES_ROOT_ROUTE,
  COMMUNITY_USERS_ROUTE,
  COMMUNITY_USER_ROUTE,
} from "../../../../routes/paths"
import * as styles from "../community.module.css"

export const UserList = () => {
  const community = communityOpenRoute.useLoaderData()
  const navigate = useNavigate()
  const linkParams = communityOpenRoute.useParams()

  const base = useParentMatches().find(
    (match) => match.fullPath === COMMUNITIES_ROOT_ROUTE,
  )?.loaderData

  const [emblaRef] = useEmblaCarousel({
    axis: "x",
    dragFree: true,
    skipSnaps: true,
  })

  const onClick = useCallback(
    (id?: number) =>
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params: {
          userId: String(id),
          communityId: String(base?.id ?? 0),
        },
      }),
    [navigate, base?.id],
  )

  return (
    <Box width="100%" overflow="hidden" id="members">
      <Box className={styles.gridTitle} pl={2} pr={2}>
        <Typography variant="h5">New members</Typography>
        <PartyLink
          to={COMMUNITY_USERS_ROUTE}
          params={{ communityId: linkParams.communityId }}
        >
          See all members
        </PartyLink>
      </Box>
      <Box overflow="hidden" width="100%" ref={emblaRef}>
        <Box display="flex" gap={2}>
          {community?.users
            .slice(0, displayUsersCount)
            .map((user) => (
              <UserRow
                labelInfo="New Member: "
                onClick={onClick}
                key={user.id}
                user={user}
                communityId={linkParams.communityId}
              />
            ))}
        </Box>
      </Box>
    </Box>
  )
}
