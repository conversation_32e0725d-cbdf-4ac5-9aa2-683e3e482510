import { Box, Divider, List } from "@mui/material"
import classnames from "classnames"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { LoginButton } from "../../../components/common/LoginButton/LoginButton"
import { PartyBox } from "../../../components/elements/link/PartyBox/PartyBox"
import { COMMUNITY_IMAGES } from "../../../config/images"
import { communityRootRoute } from "../../../routes/community/community.root.route"
import {
  COMMUNITY_EDIT_ROUTE,
  COMMUNITY_EVENTS_ROUTE,
  COMMUNITY_ROUTE,
  COMMUNITY_USERS_ROUTE,
  COMMUNITY_USER_ROUTE,
  GAMES_ROUTE,
  INDEX_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { NavigationItem } from "../components/NavigationItem"
import * as styles from "../navigation.module.css"

interface DrawerButtonsProps {
  toggleDrawer: () => void
}
export const DrawerButtons = ({ toggleDrawer }: DrawerButtonsProps) => {
  const isLoggedIn = useUserStore((state) => state.isLoggedIn)

  const comunityData = communityRootRoute.useLoaderData()
  const userInfo = useUserStore((state) => state.userData)

  const id = String(comunityData?.id ?? 0)

  const onClose = () => {
    toggleDrawer()
  }

  return (
    <Box
      onClick={onClose}
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      alignItems="center"
      gap={2}
      height="100%"
      pb={8}
    >
      {isLoggedIn && (
        <>
          <List className={styles.list}>
            <PartyBox
              to={COMMUNITY_ROUTE}
              params={{ communityId: id }}
              title={comunityData?.name ?? ""}
              className={classnames(styles.logo)}
              sx={{
                backgroundImage: `url(${ENV_IMAGE_CDN}${COMMUNITY_IMAGES}/small_${comunityData?.image ?? ""})`,
              }}
            />
            <NavigationItem
              text={comunityData?.name ?? ""}
              route={COMMUNITY_ROUTE}
              params={{ communityId: id }}
              isMain
            />
            <NavigationItem
              text="Games"
              route={GAMES_ROUTE}
              params={{ communityId: id }}
            />
            <NavigationItem
              text="Events"
              route={COMMUNITY_EVENTS_ROUTE}
              params={{ communityId: id }}
            />
            <NavigationItem
              text="Members"
              route={COMMUNITY_USERS_ROUTE}
              params={{ communityId: id }}
            />
            {hasPermission(userInfo, "community", "update", {
              id: Number(id),
            }) && (
              <NavigationItem
                text="Administrate"
                route={COMMUNITY_EDIT_ROUTE}
                params={{ communityId: id }}
              />
            )}
            <NavigationItem
              text="Profile"
              route={COMMUNITY_USER_ROUTE}
              params={{ communityId: id, userId: String(userInfo?.id ?? 0) }}
            />
          </List>
          <Divider sx={{ width: "100%" }} />
        </>
      )}
      <Box>
        <List>
          <NavigationItem text="Home" route={INDEX_ROUTE} />
        </List>
        <LoginButton />
      </Box>
    </Box>
  )
}
