import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { EVENT_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { EventGamesPage } from "../../pages/event/eventGamesPage/EventGamesPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_EVENT_MANAGE_ROUTE } from "../paths"

import { eventRootRoute } from "./event.root.route"

const eventInfoSchema = z.object({})

export const eventManageRoute = createRoute({
  validateSearch: (search) => eventInfoSchema.parse(search),
  getParentRoute: () => eventRootRoute,
  path: PART_EVENT_MANAGE_ROUTE,
  staleTime: EVENT_STALE_TIME,
  loader: async ({ context: { trpc }, params: { eventId } }) => {
    try {
      return await trpc.eventExtended.query({
        eventId: parseInt(eventId),
      })
    } catch (error) {
      return handleLoaderErrors("Can't find games", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: EventGamesPage,
})
