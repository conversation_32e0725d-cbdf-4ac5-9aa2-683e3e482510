import { Outlet, createRoute } from "@tanstack/react-router"
import { TanStackRouterDevtools } from "@tanstack/router-devtools"

import { COMMUNITIES_STALE_TIME } from "../../config/routes"
import { LayoutCommunity } from "../../layout/LayoutEvent/LayoutCommunity"
import { Navigation } from "../../layout/Navigation/Navigation"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { GeneralErrorPage } from "../../pages/common/generalErrorPage/GeneralErrorPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { COMMUNITIES_ROOT_ROUTE } from "../paths"
import { rootRoute } from "../root"

export const communityRootRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: COMMUNITIES_ROOT_ROUTE,
  staleTime: COMMUNITIES_STALE_TIME,
  loader: async ({ context: { trpc }, params: { communityId } }) => {
    try {
      return await trpc.communityBasic.query({
        communityId: parseInt(communityId),
      })
    } catch (error) {
      return handleLoaderErrors("Community not found!", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  notFoundComponent: (data) => (
    <>
      <Navigation type="community" pad />
      <LayoutCommunity>
        <GeneralErrorPage data={data} />
      </LayoutCommunity>
      {ENV_MODE === "dev" && <TanStackRouterDevtools />}
    </>
  ),
  component: () => {
    return (
      <>
        <Navigation type="community" pad />
        <LayoutCommunity>
          <Outlet />
        </LayoutCommunity>
        {ENV_MODE === "dev" && <TanStackRouterDevtools />}
      </>
    )
  },
})
