import { TRPCError } from "@trpc/server"
import { and, count, eq } from "drizzle-orm"

import { hasPermission } from "../../../../common/src/permissions/hasPermissions"
import { Event } from "../../../../common/src/permissions/objects/event"
import {
  Role,
  RoleData,
} from "../../../../common/src/permissions/roles/helpers/types"
import { db } from "../db"
import { eventSchema } from "../db/schema/event.schema"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"

import { removeRoles } from "./removeRoles"

export const insertEventRoleAndCount = async (
  userId: number,
  eventId: number,
  roleId:
    | "host"
    | "cohost"
    | "participant"
    | "interested"
    | "requested"
    | "reserved"
    | "unwelcome",
) => {
  await db.insert(permissionUserToRoleSchema).values({
    userId,
    roleId,
    subject: "event",
    subjectId: eventId,
  })

  if (roleId === "participant" || roleId === "reserved") {
    const fields = roleId === "participant" ? "going" : "reserve"

    const countTotal = await db
      .select({ count: count() })
      .from(permissionUserToRoleSchema)
      .where(
        and(
          eq(permissionUserToRoleSchema.subject, "event"),
          eq(permissionUserToRoleSchema.subjectId, eventId),
          eq(permissionUserToRoleSchema.roleId, "reserved"),
        ),
      )
      .then((count) => count[0].count)

    await db
      .update(eventSchema)
      .set({
        [fields]: countTotal,
      })
      .where(eq(eventSchema.id, eventId))
  }
}

type RoleSetProps = {
  user: { id: number; roles: RoleData[] }
  event: Event & { memberApproval: boolean }
  force?: boolean
  approveRole?: boolean
}

export const setAsParticipant = async ({
  user,
  event,
  force = false,
  approveRole = false,
}: RoleSetProps): Promise<Role> => {
  if (!event || !event.id) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  const canJoin = force || hasPermission(user, "event", "join", event)

  if (!canJoin) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You don't have permission to join this event",
    })
  }

  if (event.memberApproval && !approveRole && !force) {
    return setAsRequested({ user, event, force, approveRole })
  }

  if (
    !force &&
    (event.maxCapacity ?? 0) > 0 &&
    (event.going ?? 0) >= (event.maxCapacity ?? 0)
  ) {
    return setAsReserved({ user, event })
  }

  await insertEventRoleAndCount(user.id, event.id, "participant")

  return "participant"
}

export const setAsRequested = async ({
  user,
  event,
  force = false,
}: RoleSetProps): Promise<Role> => {
  if (!event || !event.id) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  const canJoin = force || hasPermission(user, "event", "join", event)

  if (!canJoin) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You don't have permission to join this event",
    })
  }

  const isRequested = hasPermission(user, "event", "isRequested", event)

  if (isRequested) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You have already requested to join this event",
    })
  }

  const isWaiting = hasPermission(user, "event", "isWaiting", event)

  if (isWaiting) {
    await removeRoles(user.id, "event", event.id)
  }

  await insertEventRoleAndCount(user.id, event.id, "requested")
  return "requested"
}

export const setAsReserved = async ({
  user,
  event,
  force,
}: RoleSetProps): Promise<Role> => {
  if (!event || !event.id) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  const canJoin = force || hasPermission(user, "event", "join", event)

  if (!canJoin) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You don't have permission to join this event",
    })
  }

  if (!event.reserveCapacity || (event.reserve ?? 0) >= event.reserveCapacity) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "Event is full",
    })
  }

  const isWaiting = hasPermission(user, "event", "isWaiting", event)

  if (isWaiting) {
    await removeRoles(user.id, "event", event.id)
  }

  await insertEventRoleAndCount(user.id, event.id, "reserved")
  return "reserved"
}

export const setAsInterested = async ({
  user,
  event,
  force,
}: RoleSetProps): Promise<Role> => {
  if (!event || !event.id) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  const canJoin = force || hasPermission(user, "event", "join", event)

  if (!canJoin) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You don't have permission to join this event",
    })
  }

  const isWaiting = hasPermission(user, "event", "isWaiting", event)
  const isParticipant = hasPermission(user, "event", "isParticipant", event)

  if (isParticipant || isWaiting) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You are already participant of this event",
    })
  }

  await insertEventRoleAndCount(user.id, event.id, "interested")
  return "interested"
}

export const removeEventRole = async ({
  user,
  event,
}: RoleSetProps): Promise<null> => {
  if (!event || !event.id) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  const isDenied = hasPermission(user, "event", "isDenied", event)
  if (isDenied) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Not Found: Such event does not exist",
    })
  }

  await removeRoles(user.id, "event", event.id)
  return null
}
