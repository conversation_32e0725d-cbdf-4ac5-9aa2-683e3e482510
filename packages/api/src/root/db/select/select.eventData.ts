import { eventSchema } from "../schema/event.schema"
import { usersSchema } from "../schema/users.schema"

export const selectEventData = {
  id: eventSchema.id,
  title: eventSchema.title,
  openness: eventSchema.openness,
  location: eventSchema.location,
  maxCapacity: eventSchema.maxCapacity,
  reserveCapacity: eventSchema.reserveCapacity,
  starts: eventSchema.starts,
  ends: eventSchema.ends,
  description: eventSchema.description,
  state: eventSchema.state,
  created: eventSchema.created,
  approval: eventSchema.memberApproval,
  share: eventSchema.share,
  smallDescription: eventSchema.smallDescription,
  minCapacity: eventSchema.minCapacity,
  hasAgeLimit: eventSchema.hasAgeLimit,
  going: eventSchema.going,
  reserve: eventSchema.reserve,
  hostId: usersSchema.id,
  hostName: usersSchema.name,
  image: eventSchema.image,
}
