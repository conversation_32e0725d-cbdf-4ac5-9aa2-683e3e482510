import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../common/src/permissions/hasPermissions"
import { db } from "../../db"
import { communitySchema } from "../../db/schema/community.schema"
import { communityToEventSchema } from "../../db/schema/communityToEvent.schema"
import { eventSchema } from "../../db/schema/event.schema"
import { usersSchema } from "../../db/schema/users.schema"
import { selectEventData } from "../../db/select/select.eventData"
import { selectEvents } from "../../db/select/select.events"
import { eventProcedure } from "../../trpc/procedures/eventProcedure"

import { hostInfoSchema } from "./eventsListHelper"

export const eventBasic = eventProcedure
  .input(z.object({ eventId: z.number() }))
  .query(async ({ input, ctx: { loginData } }) => {
    const event = await db
      .select({ ...selectEventData })
      .from(eventSchema)
      .leftJoin(
        hostInfoSchema,
        and(
          eq(hostInfoSchema.subject, "event"),
          eq(hostInfoSchema.roleId, "host"),
          eq(hostInfoSchema.subjectId, selectEvents.id),
        ),
      )
      .leftJoin(usersSchema, eq(hostInfoSchema.userId, usersSchema.id))
      .where(eq(eventSchema.id, input.eventId))
      .then(async (event) => {
        const communities = await db
          .select({
            id: communitySchema.id,
          })
          .from(communityToEventSchema)
          .innerJoin(
            communitySchema,
            eq(communityToEventSchema.communityId, communitySchema.id),
          )
          .where(eq(communityToEventSchema.eventId, event[0].id))
        return { ...event[0], communities }
      })

    if (!event) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No Event found",
      })
    }

    if (!hasPermission(loginData, "event", "viewPublic", event)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't view this event",
      })
    }

    const communities = await db
      .select({
        id: communitySchema.id,
        owner: communityToEventSchema.owner,
        name: communitySchema.name,
        image: communitySchema.image,
      })
      .from(communityToEventSchema)
      .innerJoin(
        communitySchema,
        eq(communityToEventSchema.communityId, communitySchema.id),
      )
      .where(eq(communityToEventSchema.eventId, event.id))
      .then((data) => {
        return data
      })

    return {
      ...event,
      communities,
    }
  })
