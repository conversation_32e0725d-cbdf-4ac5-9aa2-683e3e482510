import fs from "node:fs"
import path from "node:path"

import { TRPCError } from "@trpc/server"
import dayjs from "dayjs"
import utc from "dayjs/plugin/utc"

import { createEventInputs } from "../../../../common/src/zodSchemas/postSchemas"
import { db } from "../db"
import { communityToEventSchema } from "../db/schema/communityToEvent.schema"
import { eventSchema } from "../db/schema/event.schema"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { hasPermission } from "../permissions"
import { communityProcedure } from "../trpc/procedures/communityProcedure"
import { defaultEventPicture } from "../upload/defaultEventPicture"

export const createEvent = communityProcedure
  .input(createEventInputs)
  .mutation(async ({ input, ctx: { loginData, community } }) => {
    if (!community) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "No Community found",
      })
    }

    if (!hasPermission(loginData, "community", "event", community)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't create new event",
      })
    }

    dayjs.extend(utc)

    const insertResult = await db
      .insert(eventSchema)
      .values({
        title: input.title,
        location: input.location ?? null,
        openness: input.openness,
        memberApproval: input.approval ?? true,
        description: input.description ?? null,
        starts: dayjs(input.starts).utc().toDate(),
        ends: input.ends ? dayjs(input.ends).toDate() : null,
        maxCapacity: input.maxCapacity ?? null,
        reserveCapacity: input.reserveCapacity ?? null,
        state: input.state ?? "hidden",
        creatorId: loginData.id,
        share: input.share ?? true,
        hasAgeLimit: input.hasAgeLimit ?? false,
        minCapacity: input.minCapacity ?? null,
        smallDescription: input.smallDescription ?? null,
        image: "",
        going: 0,
        reserve: 0,
      })
      .$returningId()
      .then((re) => re[0])

    const eventId = insertResult.id

    db.insert(communityToEventSchema)
      .values({
        eventId: eventId,
        communityId: community.id,
        owner: true,
      })
      .then((re) => re)

    db.insert(permissionUserToRoleSchema)
      .values({
        userId: loginData.id,
        roleId: "host",
        subject: "event",
        subjectId: eventId,
      })
      .then((re) => re)

    db.insert(permissionUserToRoleSchema)
      .values({
        userId: loginData.id,
        roleId: "participant",
        subject: "event",
        subjectId: eventId,
      })
      .then((re) => re)

    const buffer = fs.readFileSync(
      path.resolve(__dirname, "../assets/default_event_image.png"),
    )

    await defaultEventPicture(buffer, eventId)

    const bufferThumb = fs.readFileSync(
      path.resolve(__dirname, "../assets/default_event_image_thumb.png"),
    )

    await defaultEventPicture(bufferThumb, eventId, true)

    return { success: true, eventId }
  })
