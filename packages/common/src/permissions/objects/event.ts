export type Event = {
  // subjectId: number
  id?: number
  hosts?: { id: number }[]
  state?: string
  openness?: string
  maxCapacity?: number | null
  reserveCapacity?: number | null
  going?: number | null
  reserve?: number | null
}

export interface EventPermissions {
  dataType: Event
  action:
    | "isWatching"
    | "isParticipant"
    | "isWaiting"
    | "isRequested"
    | "isHost"
    | "isDenied"
    | "isReserved"
    | "promoteCohost"
    | "view"
    | "viewPublic"
    | "update"
    | "delete"
    | "join"
    | "approve"
}
