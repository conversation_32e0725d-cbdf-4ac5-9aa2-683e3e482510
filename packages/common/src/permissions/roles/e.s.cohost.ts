import { RoleWithPermissionsAndProperties } from "../general"
import { isEventRole } from "./checks/event.isEventRole"

export const cohost: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {},
    userdata: {},
    global: {},
    event: {
      join: false,
      update: isEventRole,
      approve: isEventRole,
    },
  },
  properties: {
    subject: "event",
    level: "super",
  },
}
