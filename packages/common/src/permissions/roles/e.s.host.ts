import { RoleWithPermissionsAndProperties } from "../general"
import { eventJoin } from "./checks/event.join"
import { isEventRole } from "./checks/event.isEventRole"

export const host: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {},
    userdata: {},
    global: {},
    event: {
      join: eventJoin,
      isHost: isEventRole,
      promoteCohost: isEventRole,
      update: isEventRole,
      delete: isEventRole,
      approve: isEventRole,
    },
  },
  properties: {
    subject: "event",
    level: "super",
  },
}
