import { type Role, type RoleC, type RoleData, type Subject } from "./types";

export const hasSubjectRole = (
  roles: RoleData[],
  role: Role,
  subject: Subject,
  subjectId?: number,
) =>
  roles.findIndex(
    (currentRole) =>
      role === currentRole.role &&
      currentRole.subject === subject &&
      currentRole.subjectId === subjectId,
  ) > -1;

export const hasCommunityRole = (
  roles: RoleData[] | undefined | null,
  role: RoleC | RoleC[],
  subjectId: number,
) => {
  if (!roles) return false;
  if (typeof role !== "string") {
    return role.some((r) => hasSubjectRole(roles, r, "community", subjectId));
  } else {
    return hasSubjectRole(roles, role, "community", subjectId);
  }
};

export const getSubjectRole = (
  subjectId: number,
  subject: Subject,
  roles: RoleData[],
) => {
  const role = roles.find(
    (r) => r.subject === subject && r.subjectId === subjectId,
  );
  if (role) return role.role;
  return null;
};

export const getEventRole = (subjectId: number, roles: RoleData[]) =>
  getSubjectRole(subjectId, "event", roles);
