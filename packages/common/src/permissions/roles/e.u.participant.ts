import { RoleWithPermissionsAndProperties } from "../general"
import { isEventRole } from "./checks/event.isEventRole"

export const participant: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {},
    userdata: {},
    global: {},
    event: {
      join: false,
      isParticipant: isEventRole,
      view: isEventRole,
      viewPublic: isEventRole,
    },
  },
  properties: {
    subject: "event",
    level: "user",
  },
}
